#!/usr/bin/env python3
"""
Figma S3 Image Extractor
Extracts content images from Figma design files.
"""

import json
import yaml
import re
from typing import List, Dict, Any
from pathlib import Path
import argparse


class FigmaS3Extractor:
    def __init__(self):
        pass
    
    def is_content_image(self, element: Dict[str, Any]) -> bool:
        """Check if element is a content image."""
        # Must have imageUrl
        if not element.get('imageUrl'):
            return False
        
        # Allow all visual shape types that can contain images
        allowed_types = {'rectangle', 'ellipse', 'polygon', 'star', 'frame'}
        allowed_figma_types = {'RECTANGLE', 'ELLIPSE', 'POLYGON', 'STAR', 'FRAME'}
        
        element_type = element.get('type', '').lower()
        figma_type = element.get('figma_type', '')
        
        # Must be a visual shape type
        if not (element_type in allowed_types or figma_type in allowed_figma_types):
            return False
        
        # Skip component instances
        if element.get('component_ref'):
            return False
        
        # Skip component nested elements
        element_id = element.get('id', '')
        if element_id.startswith('I') and ';' in element_id:
            return False
        
        name = element.get('name', '').lower()
        
        # Only include elements named "image" + space + number
        if re.match(r'^image\s+\d+$', name):
            return True
        
        return False
    
    def extract_from_element(self, element: Dict[str, Any], results: List[Dict[str, str]]):
        """Recursively extract images from element tree."""
        if self.is_content_image(element):
            results.append({
                'name': element.get('name', 'Unknown'),
                'id': element.get('id', ''),
                'type': element.get('type', ''),
                'figma_type': element.get('figma_type', ''),
                's3_url': element.get('imageUrl', '')
            })
        
        # Check children
        for child in element.get('children', []):
            self.extract_from_element(child, results)
    
    def extract_s3_urls(self, figma_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """Extract all content image S3 URLs."""
        results = []
        
        # Find root element
        if 'root' in figma_data:
            self.extract_from_element(figma_data['root'], results)
        elif 'children' in figma_data:
            self.extract_from_element(figma_data, results)
        elif isinstance(figma_data, list):
            for item in figma_data:
                if isinstance(item, dict):
                    self.extract_from_element(item, results)
        
        return results
    
    def load_file(self, file_path: str) -> Dict[str, Any]:
        """Load YAML or JSON file."""
        path = Path(file_path)
        with open(path, 'r', encoding='utf-8') as f:
            if path.suffix.lower() in ['.yaml', '.yml']:
                return yaml.safe_load(f)
            else:
                return json.load(f)
    
    def process_file(self, input_path: str, output_path: str = None) -> List[str]:
        """Process file and return S3 URLs."""
        figma_data = self.load_file(input_path)
        results = self.extract_s3_urls(figma_data)
        
        s3_urls = [img['s3_url'] for img in results]
        
        if output_path:
            output_data = {
                'total_images': len(results),
                'images': results,
                's3_urls': s3_urls
            }
            with open(output_path, 'w') as f:
                json.dump(output_data, f, indent=2)
            print(f"Saved {len(s3_urls)} images to {output_path}")
        else:
            print(f"Found {len(s3_urls)} images:")
            for result in results:
                print(f"{result['name']} -> {result['s3_url']}")
        
        return s3_urls


def main():
    parser = argparse.ArgumentParser(description='Extract S3 image URLs from Figma files')
    parser.add_argument('input_file', help='Input YAML/JSON file')
    parser.add_argument('-o', '--output', help='Output JSON file')
    parser.add_argument('--urls-only', action='store_true', help='Print only URLs')
    
    args = parser.parse_args()
    
    extractor = FigmaS3Extractor()
    urls = extractor.process_file(args.input_file, args.output)
    
    if args.urls_only:
        for url in urls:
            print(url)


if __name__ == '__main__':
    main()